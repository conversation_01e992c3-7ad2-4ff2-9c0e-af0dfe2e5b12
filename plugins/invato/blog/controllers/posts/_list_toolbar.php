<?php

use Invato\Blog\Models\BlogSetting;

?>
<div data-control="toolbar">
    <a
        href="<?= Backend::url('invato/blog/posts/create') ?>"
        class="btn btn-primary oc-icon-plus">
        <?= e(trans('backend::lang.form.create')) ?>
    </a>

    <?php
    $blogSettings = BlogSetting::instance();
if (isset($blogSettings['syncWithDomain'], $blogSettings['authToken'])) { ?>
        <button
            class="btn btn-outline-info oc-icon-refresh"
            data-request="onSync"
            data-request-confirm="<?= e(trans('Are you sure you want to synchronize?')) ?>"
            data-stripe-load-indicator>
            <?= e(trans('Synchronize')) ?>
        </button>
    <?php } ?>

    <button
        class="btn btn-default oc-icon-trash-o"
        data-request="onDelete"
        data-request-confirm="<?= e(trans('backend::lang.list.delete_selected_confirm')) ?>"
        data-list-checked-trigger
        data-list-checked-request
        data-stripe-load-indicator>
        <?= e(trans('backend::lang.list.delete_selected')) ?>
    </button>
</div>
