<?php

namespace Invato\Blog\Controllers;

use Artisan;
use Backend\Behaviors\FormController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Illuminate\Support\Str;
use Invato\Blog\Models\Post;
use October\Rain\Support\Facades\Flash;
use RuntimeException;

class Posts extends Controller
{
    public $implement = [
        FormController::class,
        ListController::class,
    ];

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public $requiredPermissions = [
        'invato.blog.manage_posts',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Blog', 'main-menu-item', 'side-menu-item2');
    }

    public function onDuplicate()
    {
        if (! $id = post('id')) {
            throw new RuntimeException('ID not specified');
        }

        $event = Post::find($id);
        if (! $event) {
            throw new RuntimeException('Event not found');
        }

        $newModel = $event->replicate();
        $newModel->slug = $event->slug.'-'.Str::random(5);
        $newModel->save();

        Flash::success(trans('invato.blog::lang.post.duplicate_success'));

        return $this->listRefresh();
    }

    public function onSync()
    {
        // Voer de synchronisatie uit
        $result = Artisan::call('invato:blog:sync-from-api-endpoint');

        // Toon een flash bericht
        \Flash::success('Blog posts zijn gesynchroniseerd.');

        // Herlaad de lijst
        return $this->listRefresh();
    }
}
