plugin:
    name: 'invato.blog::lang.plugin.name'
    description: 'invato.blog::lang.plugin.description'
    author: Invato
    icon: oc-icon-comment-o
    homepage: ''
permissions:
    'invato.blog.manage_blog':
        tab: 'invato.blog::lang.plugin.name'
        label: 'invato.blog::lang.permission.blog'
    'invato.blog.manage_categories':
        tab: 'invato.blog::lang.plugin.name'
        label: 'invato.blog::lang.permission.categories'
    'invato.blog.manage_posts':
        tab: 'invato.blog::lang.plugin.name'
        label: 'invato.blog::lang.permission.posts'
    'invato.blog.manage_authors':
        tab: 'invato.blog::lang.plugin.name'
        label: 'invato.blog::lang.permission.authors'
navigation:
    main-menu-item:
        label: 'invato.blog::lang.plugin.name'
        url: /
        icon: icon-comment-o
        iconSvg: plugins/invato/blog/assets/images/invato-blog.svg
        permissions:
            - 'invato.blog.manage_blog'
        sideMenu:
            side-menu-item:
                label: 'invato.blog::lang.global.categories'
                url: invato/blog/categories
                icon: icon-tags
                permissions:
                    - 'invato.blog.manage_categories'
            side-menu-item2:
                label: 'invato.blog::lang.global.posts'
                url: invato/blog/posts
                icon: icon-file-text
                permissions:
                    - 'invato.blog.manage_posts'
            side-menu-item3:
                label: 'invato.blog::lang.global.authors'
                url: invato/blog/authors
                icon: icon-user
                permissions:
                    - 'invato.blog.manage_authors'
