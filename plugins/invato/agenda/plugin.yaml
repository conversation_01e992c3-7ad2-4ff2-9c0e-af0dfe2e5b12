plugin:
    name: 'invato.agenda::lang.plugin.name'
    description: 'invato.agenda::lang.plugin.description'
    author: Invato
    icon: oc-icon-calendar
    homepage: ''
permissions:
    'invato.agenda.manage_agenda':
        tab: 'invato.agenda::lang.plugin.name'
        label: 'invato.agenda::lang.permissions.manage_agenda'
    'invato.agenda.import_events':
        tab: 'invato.agenda::lang.plugin.name'
        label: 'invato.agenda::lang.permissions.import_events'
    'invato.agenda.export_events':
        tab: 'invato.agenda::lang.plugin.name'
        label: 'invato.agenda::lang.permissions.export_events'
navigation:
    agenda:
        label: 'invato.agenda::lang.plugin.name'
        url: /
        icon: icon-calendar
        iconSvg: plugins/invato/agenda/assets/images/invato-agenda.svg
        permissions:
            - 'invato.agenda.manage_agenda'
        sideMenu:
            content-section:
                label: 'Content'
                itemType: section

            events:
                label: 'invato.agenda::lang.menu.events'
                url: invato/agenda/events
                icon: icon-calendar
                permissions:
                    - 'invato.agenda.manage_agenda'

            settings-section:
                label: 'Settings'
                itemType: section

            settings:
                label: 'General'
                url: system/settings/update/invato/agenda/settings
                icon: icon-cogs
                permissions:
                    - 'invato.agenda.manage_agenda'

            documentation-section:
                label: 'Documentation'
                itemType: section

            readme:
                label: 'Readme'
                url: invato/agenda/readme
                icon: icon-book
                permissions:
                    - 'superusers.view_readme'
            manual:
                label: 'Manual'
                url: invato/agenda/manual
                icon: icon-book
                permissions:
                    - 'invato.agenda.manage_agenda'
