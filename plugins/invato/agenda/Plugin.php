<?php

namespace Invato\Agenda;

use Invato\Agenda\Components\EventDetail;
use Invato\Agenda\Components\EventFeed;
use Invato\Agenda\Models\AgendaSettings;
use Invato\Agenda\Models\Event;
use Invato\Traits\RegistersPageFinderTrait;
use Invato\Traits\SettingsMenuContextTrait;
use System\Classes\PluginBase;

/**
 * Agenda Plugin
 */
class Plugin extends PluginBase
{
    use RegistersPageFinderTrait;
    use SettingsMenuContextTrait;

    public function boot(): void
    {
        $this->setupSettingsMenuContext('settings');
        $this->registerPageFinder();
    }

    public function register(): void {}

    public function registerComponents(): array
    {
        return [
            EventDetail::class => 'EventDetail',
            EventFeed::class => 'EventFeed',
        ];
    }

    public function registerSettings(): array
    {
        return [
            'settings' => [
                'label' => trans('invato.agenda::lang.settings.label'),
                'description' => trans('invato.agenda::lang.settings.description'),
                'category' => 'Plugins',
                'icon' => 'icon-calendar',
                'class' => AgendaSettings::class,
            ],
        ];
    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => Event::class,
            'menu_types' => [
                'agenda-event' => 'invato.agenda::lang.menuitem.agenda_event',
                'all-agenda-events' => 'invato.agenda::lang.menuitem.all_agenda_events',
            ],
        ];
    }
}
