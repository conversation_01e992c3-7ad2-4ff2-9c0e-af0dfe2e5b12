<?php

namespace Invato\Agenda\Models;

use Backend\Models\ImportModel;
use Exception;

class EventImportModel extends ImportModel
{
    public $rules = [
        'title' => 'required|string|max:255',
        'date' => 'required|date',
    ];

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if event with this title/slug already exists
                $slug = $data['slug'] ?? str_slug($data['title']);
                $event = Event::withTrashed()->where('slug', $slug)->first();

                if (! $event) {
                    $event = new Event;
                }

                $event->fill([
                    'title' => $data['title'] ?? null,
                    'slug' => $slug ?? null,
                    'is_active' => $data['is_active'] ?? true,
                    'date' => $data['date'] ?? null,
                    'date_end' => $data['date_end'] ?? null,
                    'location' => $data['location'] ?? null,
                    'time_start' => $data['time_start'] ?? null,
                    'time_end' => $data['time_end'] ?? null,
                    'excerpt' => $data['excerpt'] ?? null,
                    'text' => $data['text'] ?? null,
                    'overview_image' => $data['overview_image'] ?? null,
                ]);

                $event->save();

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
