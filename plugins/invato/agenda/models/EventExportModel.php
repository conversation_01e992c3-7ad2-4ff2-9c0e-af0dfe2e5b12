<?php

namespace Invato\Agenda\Models;

use Backend\Models\ExportModel;

class EventExportModel extends ExportModel
{
    public function exportData($columns, $sessionKey = null): array
    {
        $events = Event::all();
        $exportData = [];

        foreach ($events as $event) {
            $record = [];

            foreach ($columns as $column) {
                // Handle special cases
                $record[$column] = match ($column) {
                    'date' => $event->date ? $event->date->format('Y-m-d') : null,
                    'date_end' => $event->date_end ? $event->date_end->format('Y-m-d') : null,
                    default => $event->{$column},
                };
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
