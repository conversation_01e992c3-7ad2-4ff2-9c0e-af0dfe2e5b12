<?php

namespace Invato\Agenda\Models;

use Invato\Agenda\Components\EventDetail;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Seo\traits\HasSeoableTrait;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\Validation;
use RainLab\Translate\Behaviors\TranslatableModel;

/**
 * Event Model
 */
class Event extends Model
{
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use HasSeoableTrait;
    use SoftDelete;
    use Sluggable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function ($event) {
            static::createRedirect(
                plugin: 'agenda',
                modelRecord: $event,
                detailPageController: EventDetail::class,
                status: 301
            );
        });

        static::restored(static function ($event) {
            static::deleteRedirect($event);
        });
    }

    public $implement = [
        TranslatableModel::class,
    ];

    public $translatable = ['title', 'location', 'excerpt', 'text'];

    protected $dates = ['deleted_at', 'date', 'date_end'];

    protected $casts = [
        'is_active' => 'boolean',
        'date' => 'date',
        'date_end' => 'date',
    ];

    protected $fillable = [
        'title',
        'slug',
        'is_active',
        'date',
        'date_end',
        'location',
        'time_start',
        'time_end',
        'excerpt',
        'text',
        'overview_image',
    ];

    protected $slugs = [
        'slug' => 'title',
    ];

    public $table = 'invato_agenda_events';

    public $rules = [
        'title' => 'required|string|max:255',
        'slug' => 'required|string|max:255|unique:invato_agenda_events,slug',
        'date' => 'required|date',
        'date_end' => 'nullable|date|after_or_equal:date',
        'time_start' => 'nullable|string',
        'time_end' => 'nullable|string',
        'is_active' => 'boolean',
        'location' => 'nullable|string|max:255',
        'excerpt' => 'nullable|string',
        'text' => 'nullable|string',
        'overview_image' => 'nullable|string',
    ];

    /**
     * Get PageFinder configuration for this model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'agenda-event',
            'all_type' => 'all-agenda-events',
            'component' => 'EventDetail',
        ];
    }
}
