<?php

namespace Invato\Agenda\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Invato\Agenda\Models\Event;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;

class Events extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static string $modelClass = Event::class;

    public string $formConfig = 'config_form.yaml';

    public string $listConfig = 'config_list.yaml';

    public string $importExportConfig = 'config_import_export.yaml';

    public string $importPermission = 'invato.agenda.import_events';

    public string $exportPermission = 'invato.agenda.export_events';

    public $requiredPermissions = [
        'invato.agenda.manage_agenda',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Agenda', 'agenda', 'events');
    }
}
