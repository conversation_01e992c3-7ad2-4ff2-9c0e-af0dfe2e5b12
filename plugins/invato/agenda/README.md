# Interne documentatie

## Doel:

Het beheren en tonen van evenementen/agenda items op de website, met mogelijkheden voor het plannen van events, 
het tonen van event details en het beheren van event informatie.

## Backend:

De plugin heeft 2 backend pagina's, die in het linkermenu onder kopje Agenda vallen: Evenementen en Instellingen.

### Evenementen pagina:

Hier kunnen evenementen worden aangemaakt, bewerkt en verwijderd. Elk evenement heeft de volgende eigenschappen:
- Titel en URL slug
- Start- en einddatum
- Start- en eindtijd
- Locatie
- Korte beschrijving (excerpt)
- Volledige beschrijving
- Overzicht afbeelding
- Actief/inactief status

### Wie heeft toegang?

* Invato
* Klant met toegang tot de plugin.

### Instellingen

Hier staan de opties voor de plugin, zoals de pagina die als overzicht van evenementen wordt gebruikt en 
de detailpagina voor individuele evenementen.

### Wie heeft toegang?

* Invato

## Front-end:

Deze plugin bevat 2 componenten die gebruikt kunnen worden op CMS pagina's:

1. **EventFeed**: Toont een overzicht van evenementen
2. **EventDetail**: Toont details van een specifiek evenement

### Wie heeft toegang?

* Invato
* Klant met toegang tot de plugin.

## Plugin uitbreiden (Extensie plugin)

De agenda plugin kan met een extensie plugin gemakkelijk worden uitgebreid met nieuwe functies, 
componenten en extra velden.

### Extra velden toevoegen

Om extra velden aan te maken, kunnen deze worden toegevoegd via de `backend.form.extendFields` event 
in de boot() method van een extensie plugin:

```php
Event::listen('backend.form.extendFields', function ($widget) {
    if (
        !$widget->model instanceof \Invato\Agenda\Models\Event
    ) {
        return;
    }

    if ($widget->isNested) {
        return;
    }

    $widget->addFields([
        'custom_field' => [
            'label' => 'Custom Field',
            'type' => 'text',
        ],
    ]);
});
```

## Import/Export functionaliteit

De plugin ondersteunt het importeren en exporteren van evenementen via CSV bestanden. 
Dit is handig voor bulk operaties en data migratie.

### Import velden:
- title (verplicht)
- slug
- is_active
- date (verplicht)
- date_end
- location
- time_start
- time_end
- excerpt
- text
- overview_image

### Export velden:
Alle bovenstaande velden plus created_at en updated_at timestamps.

## Soft Delete functionaliteit

De plugin ondersteunt soft deletes, wat betekent dat verwijderde evenementen kunnen worden hersteld. 
Verwijderde items zijn zichtbaar in de lijst wanneer de "Show deleted" filter is ingeschakeld.

## Technische integraties

De plugin integreert met de volgende andere plugins (indien geïnstalleerd):

- **Invato Redirects**: Automatische redirects wanneer een evenement wordt verwijderd
- **Invato SEO**: Ondersteuning voor SEO-optimalisatie van evenementen
- **RainLab Translate**: Meertalige ondersteuning voor evenement content

## Gebruikte permissions:

- `invato.agenda.manage_agenda`: Algemeen beheer van agenda functionaliteit
- `invato.agenda.import_events`: Importeren van evenementen
- `invato.agenda.export_events`: Exporteren van evenementen
- `superusers.view_readme`: Toegang tot deze README documentatie

---

Voor technische ondersteuning of vragen over deze plugin, neem contact op met [Invato](https://invato.nl/contact).
